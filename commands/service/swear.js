const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { hasDiscordPermission } = require('../../utils/permissions');
const { runHelpCommand } = require('../../utils/commandProcessor');
const { findUser } = require('../../handlers/finder');
const { ensureGuildData } = require('../../database');
const { createPagination, createConfirmation } = require('../../core/buttons');
const guildCache = require('../../database/cache/models/guild');
const config = require('../../config/setup');

// Simple swear words list
const SWEAR_WORDS = [
  'fuck', 'shit', 'bitch', 'ass', 'bastard', 'dick', 'pussy', 'cunt', 'slut', 'whore',
  'nigga', 'nigger', 'faggot', 'retard', 'retarded', 'damn', 'crap', 'cock', 'motherfucker',
  'twat', 'douche', 'cum', 'rape', 'rapist', 'anal', 'boobs', 'tits', 'tit', 'milf',
  'sex', 'porn', 'nude', 'boner', 'orgy', 'gay', 'lesbian', 'thot', 'peen', 'dildo',
  'suck', 'balls', 'blowjob', 'jerk', 'hoe', 'kys', 'shemale', 'penis', 'buttfuck',
  'booba', 'femboy', 'horny', 'masturbate', 'orgasm', 'vagina', 'bobs', 'nudes'
];

// Simple swear detection - just check if any swear word appears in the message
function detectSwears(text) {
  const foundSwears = [];
  const lowerText = text.toLowerCase();

  for (const swear of SWEAR_WORDS) {
    if (lowerText.includes(swear)) {
      foundSwears.push(swear);
    }
  }

  return [...new Set(foundSwears)]; // Remove duplicates
}

module.exports = {
  name: "swear",
  aliases: ['swears'],
  description: `track swear usage in the server`,
  usage: '{guildprefix}swear\n{guildprefix}swear list\n{guildprefix}swear [user]\n{guildprefix}swear leaderboard\n{guildprefix}swear reset',
  run: async(client, message, args) => {

    // Ensure guild data exists in the database
    await ensureGuildData(message.guild.id);

    if (args[0] === 'list') {
      // Show user's own swear list
      try {
        const userData = await guildCache.getUserSwearData(message.guild.id, message.author.id);
        
        if (!userData || userData.swears.length === 0) {
          return embeds.warn(message, 'You have no recorded swears in this server');
        }

        // Filter out words with 0 count and sort by count (highest first)
        const usedSwears = userData.swears.filter(swear => swear.count > 0);
        const sortedSwears = usedSwears.sort((a, b) => b.count - a.count);

        if (sortedSwears.length === 0) {
          return embeds.warn(message, 'You have no recorded swears in this server');
        }

        // Calculate actual total from individual word counts
        const calculatedTotal = usedSwears.reduce((total, swear) => total + swear.count, 0);

        // Format page function for pagination
        const formatPage = (pageSwears, currentPage, totalPages) => {
          const swearsList = pageSwears.map((swear, index) => {
            const number = ((currentPage - 1) * 10 + index + 1).toString().padStart(2, '0');
            return `\`${number}\` **${swear.word}** - ${swear.count}`;
          }).join('\n');

          // Use calculated total instead of stored total to fix discrepancy
          const footerText = `You have sweared ${userData.todaySwears} times today • Total: ${calculatedTotal} • Page ${currentPage}/${totalPages}`;

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle(`${message.author.username}'s Swear List`)
            .setDescription(swearsList)
            .setFooter({ text: footerText });
        };

        // Use pagination system with 10 items per page
        await createPagination(message, sortedSwears, formatPage, 10, 'Swear List');
      } catch (error) {
        console.error('Error getting swear list:', error);
        return embeds.deny(message, 'Failed to retrieve swear list');
      }

    } else if (args[0] === 'leaderboard' || args[0] === 'lb') {
      // Show server swear leaderboard
      try {
        const leaderboard = await guildCache.getSwearLeaderboard(message.guild.id);
        
        if (leaderboard.length === 0) {
          return embeds.warn(message, 'No swear data found for this server');
        }

        const userPosition = await guildCache.getUserSwearPosition(message.guild.id, message.author.id);

        // Format page function for pagination
        const formatPage = (pageUsers, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 10;

          const leaderboardList = pageUsers.map((userData, index) => {
            const position = startIndex + index + 1;
            const member = message.guild.members.cache.get(userData.userId);
            let username = 'Unknown User';

            if (member) {
              username = member.displayName;
            } else {
              // Try to get user from client cache if not in guild
              const user = client.users.cache.get(userData.userId);
              if (user) {
                username = user.username;
              }
            }

            return `\`${position.toString().padStart(2, '0')}\` **${username}** - ${userData.calculatedTotal} swears`;
          }).join('\n');

          const footerText = userPosition 
            ? `Your position: #${userPosition} • Page ${currentPage}/${totalPages}`
            : `Page ${currentPage}/${totalPages}`;

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle('Swear Leaderboard')
            .setDescription(leaderboardList)
            .setFooter({ text: footerText });
        };

        // Use pagination system with 10 items per page
        await createPagination(message, leaderboard, formatPage, 10, 'Swear Leaderboard');
      } catch (error) {
        console.error('Error getting swear leaderboard:', error);
        return embeds.deny(message, 'Failed to retrieve swear leaderboard');
      }

    } else if (args[0] === 'reset') {
      // Reset all swear data (admin only)
      if (!hasDiscordPermission(message, PermissionFlagsBits.Administrator, 'Administrator')) return;

      try {
        const swearData = await guildCache.getSwearTracker(message.guild.id);

        if (swearData.length === 0) {
          return embeds.warn(message, 'No swear data to reset');
        }

        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **reset all swear data** for this server?',
          async (interaction) => {
            // On approve: clear all swear data
            await guildCache.clearAllSwearData(message.guild.id);

            // Edit the confirmation embed to show success message
            const successEmbed = embeds.createSuccess(message, 'Reset all swear data for this server');

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } catch (error) {
        console.error('Error resetting swear data:', error);
        return embeds.deny(message, 'Failed to reset swear data');
      }

    } else if (args[0]) {
      // Handle "swears [user]" format - check if first arg is a user
      const userResult = await findUser(message.guild, args[0], client);

      if (userResult.found) {
        // Show specific user's swear stats
        const targetUser = userResult.user;
        const userId = targetUser.id || targetUser.user?.id;

        try {
          const userData = await guildCache.getUserSwearData(message.guild.id, userId);

          if (!userData || !userData.swears || userData.swears.length === 0) {
            const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
            return embeds.info(message, `**${username}** has no recorded swears in this server`);
          }

          // Calculate actual total from individual word counts
          const calculatedTotal = userData.swears.reduce((total, swear) => total + swear.count, 0);

          if (calculatedTotal === 0) {
            const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
            return embeds.info(message, `**${username}** has no recorded swears in this server`);
          }

          const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
          return embeds.info(message, `**${username}** has sweared **${calculatedTotal}** times`);
        } catch (error) {
          console.error('Error getting user swear data:', error);
          return embeds.deny(message, 'Failed to retrieve user swear data');
        }
      } else {
        // If not a user, show help
        return runHelpCommand(message, 'swear');
      }

    } else {
      // Show user's own swear stats
      try {
        const userData = await guildCache.getUserSwearData(message.guild.id, message.author.id);

        if (!userData || !userData.swears || userData.swears.length === 0) {
          return embeds.info(message, 'You have no recorded swears in this server');
        }

        // Calculate actual total from individual word counts
        const calculatedTotal = userData.swears.reduce((total, swear) => total + swear.count, 0);

        if (calculatedTotal === 0) {
          return embeds.info(message, 'You have no recorded swears in this server');
        }

        return embeds.info(message, `You have sweared **${calculatedTotal}** times`);
      } catch (error) {
        console.error('Error getting swear data:', error);
        return embeds.deny(message, 'Failed to retrieve swear data');
      }
    }
  },

  // Export the detectSwears function for use in message events
  detectSwears
}
