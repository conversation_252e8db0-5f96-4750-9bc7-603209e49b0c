// Birthday check handler - runs daily to manage birthday roles

module.exports = {
  name: 'ready',
  once: true,
  async execute(client) {
    // Set up daily birthday check
    setupBirthdayCheck(client);
  }
};

function setupBirthdayCheck(client) {
  // Run birthday check every hour (3600000 ms)
  setInterval(async () => {
    await checkBirthdays(client);
  }, 3600000); // 1 hour

  // Also run immediately on startup
  setTimeout(() => checkBirthdays(client), 5000); // 5 seconds after startup
}

async function checkBirthdays(client) {
  try {
    const userCache = require('../../database/cache/models/user');
    const guildCache = require('../../database/cache/models/guild');

    // Get all guilds the bot is in
    for (const guild of client.guilds.cache.values()) {
      try {
        // Get birthday role for this guild
        const birthdayRoleId = await guildCache.getBirthdayRole(guild.id);
        if (!birthdayRoleId) continue; // No birthday role set

        const birthdayRole = guild.roles.cache.get(birthdayRoleId);
        if (!birthdayRole) {
          // Role was deleted, clear it from settings
          await guildCache.removeBirthdayRole(guild.id);
          continue;
        }

        // Check all members in the guild
        for (const member of guild.members.cache.values()) {
          try {
            if (member.user.bot) continue; // Skip bots

            const isBirthday = await userCache.isBirthday(member.user.id);
            const hasBirthdayRole = member.roles.cache.has(birthdayRoleId);

            if (isBirthday && !hasBirthdayRole) {
              // It's their birthday and they don't have the role - give it
              try {
                await member.roles.add(birthdayRole, 'Birthday role - automatic assignment');
                console.log(`Added birthday role to ${member.user.username} in ${guild.name}`);
              } catch (roleError) {
                // Silent fail if can't add role (permissions, hierarchy, etc.)
              }
            } else if (!isBirthday && hasBirthdayRole) {
              // It's not their birthday but they have the role - remove it
              try {
                await member.roles.remove(birthdayRole, 'Birthday role - automatic removal');
                console.log(`Removed birthday role from ${member.user.username} in ${guild.name}`);
              } catch (roleError) {
                // Silent fail if can't remove role (permissions, hierarchy, etc.)
              }
            }
          } catch (memberError) {
            // Silent fail for individual member processing
          }
        }
      } catch (guildError) {
        // Silent fail for individual guild processing
      }
    }
  } catch (error) {
    // Silent fail for birthday check system
    console.error('Error in birthday check:', error);
  }
}
