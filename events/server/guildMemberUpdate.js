// Guild member update handler for tracking nickname changes

module.exports = {
  name: 'guildMemberUpdate',
  async execute(oldMember, newMember) {
    try {
      // Skip if no nickname change
      if (oldMember.nickname === newMember.nickname) {
        return;
      }

      const userCache = require('../../database/cache/models/user');
      const { ensureUserData } = require('../../database');

      // Ensure user data exists
      await ensureUserData(newMember.user.id);

      // Track nickname changes
      const oldNickname = oldMember.nickname || oldMember.user.username;
      const newNickname = newMember.nickname || newMember.user.username;

      await userCache.addNameChange(
        newMember.user.id,
        'nickname',
        oldNickname,
        newNickname,
        newMember.guild.id // Guild-specific change
      );

    } catch (error) {
      // Silent fail for nickname tracking
      console.error('Error tracking nickname change:', error);
    }
  }
}
