const mongoose = require('mongoose');

const guildSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    unique: true
  },
  Prefix: {
    type: String,
    default: ','
  },
  // Welcome system
  WelcomeChannel: {
    type: String,
    default: null
  },
  WelcomeMessage: {
    type: String,
    default: null
  },
  // Leave system
  LeaveChannel: {
    type: String,
    default: null
  },
  LeaveMessage: {
    type: String,
    default: null
  },
  // Boost system
  BoostChannel: {
    type: String,
    default: null
  },
  BoostMessage: {
    type: String,
    default: null
  },
  BoostRole: {
    type: String,
    default: null
  },
  // Jail system
  JailRoleID: {
    type: String,
    default: null
  },
  JailChannelID: {
    type: String,
    default: null
  },
  // Vanity system
  Vanity: {
    type: String,
    default: null
  },
  VanityRoles: {
    type: [String],
    default: []
  },
  VanityMessage: {
    type: String,
    default: null
  },
  VanityLogChannel: {
    type: String,
    default: null
  },
  // Auto roles
  AutoRoles: {
    type: [String],
    default: []
  },
  // Auto react
  AutoReact: [{
    word: {
      type: String,
      required: true
    },
    emojiId: {
      type: String,
      default: null
    },
    emojiName: {
      type: String,
      required: true
    },
    isCustom: {
      type: Boolean,
      default: false
    }
  }],
  // Auto responder
  AutoResponder: [{
    trigger: {
      type: String,
      required: true
    },
    response: {
      type: String,
      required: true
    },
    reply: {
      type: Boolean,
      default: false
    },
    strict: {
      type: Boolean,
      default: true
    },
    delete: {
      type: Boolean,
      default: false
    },
    selfDestruct: {
      type: Number,
      default: 0
    }
  }],
  // Force nick
  ForceNick: [{
    userId: {
      type: String,
      required: true
    },
    nickname: {
      type: String,
      required: true
    },
    setBy: {
      type: String,
      required: true
    },
    setAt: {
      type: Date,
      default: Date.now
    }
  }],
  // AFK system
  AFK: [{
    userId: {
      type: String,
      required: true
    },
    message: {
      type: String,
      default: 'AFK'
    },
    since: {
      type: Date,
      default: Date.now
    }
  }],

  // Moderation system
  Moderation: [{
    caseId: {
      type: Number,
      required: true
    },
    userId: {
      type: String,
      required: true
    },
    moderatorId: {
      type: String,
      required: true
    },
    action: {
      type: String,
      required: true,
      enum: ['warn', 'unwarn', 'mute', 'unmute', 'kick', 'ban', 'unban', 'jail', 'unjail', 'role_save']
    },
    reason: {
      type: String,
      required: true
    },
    duration: {
      type: String,
      default: null
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // AutoMod Filter system
  AutoModFilter: {
    enabled: {
      type: Boolean,
      default: false
    },
    ruleId: {
      type: String,
      default: null
    },
    keywords: {
      type: [String],
      default: []
    },
    bypassRoles: {
      type: [String],
      default: []
    },
    action: {
      type: String,
      enum: ['delete', 'timeout', 'alert'],
      default: 'delete'
    },
    timeoutDuration: {
      type: Number,
      default: 300 // 5 minutes in seconds
    }
  },

  // Last.fm crowns system
  LastFMCrowns: [{
    artist: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true
    },
    plays: {
      type: Number,
      required: true
    },
    claimedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Swear tracking system (per server)
  SwearTracker: [{
    userId: {
      type: String,
      required: true
    },
    swears: [{
      word: {
        type: String,
        required: true
      },
      count: {
        type: Number,
        default: 1
      },
      lastUsed: {
        type: Date,
        default: Date.now
      }
    }],
    totalSwears: {
      type: Number,
      default: 0
    },
    todaySwears: {
      type: Number,
      default: 0
    },
    lastSwearDate: {
      type: Date,
      default: Date.now
    }
  }],

  // Guild name history tracking
  GuildNameHistory: [{
    oldName: {
      type: String,
      required: true
    },
    newName: {
      type: String,
      required: true
    },
    changedBy: {
      type: String,
      default: null // User ID who changed the name (if available)
    },
    changedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Birthday system
  BirthdayRole: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Create indexes for better performance
guildSchema.index({ GuildID: 1 });
guildSchema.index({ 'LastFMCrowns.artist': 1 });
guildSchema.index({ 'LastFMCrowns.userId': 1 });

module.exports = mongoose.model('Guild', guildSchema);
