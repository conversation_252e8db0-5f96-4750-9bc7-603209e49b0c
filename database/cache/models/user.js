const BaseCache = require('../base');

class User {
  constructor() {
    // Automatic cache for user data
    this.cache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour TTL
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'User', // Database model name
      keyField: 'User<PERSON>', // Primary key field
      fieldsToCache: ['SelfPrefix', 'Timezone', 'LastFMUsername', 'LastFMLibrary', 'LastFMSettings', 'TicTacToe', 'Gambling', 'Roleplay', 'Notes', 'NameHistory', 'Birthday'] // Fields to cache
    });
  }

  // Get LastFM username - automatic cache handling
  async getLastFMUsername(userId) {
    return await this.cache.getField(userId, 'LastFMUsername');
  }

  // Set LastFM username - automatic database and cache update
  async setLastFMUsername(userId, lastfmUsername) {
    return await this.cache.setField(userId, 'LastFMUsername', lastfmUsername);
  }

  // Get user self prefix - automatic cache handling
  async getSelfPrefix(userId) {
    return await this.cache.getField(userId, 'SelfPrefix');
  }

  // Set user self prefix - automatic database and cache update
  async setSelfPrefix(userId, prefix) {
    return await this.cache.setField(userId, 'SelfPrefix', prefix);
  }

  // Clear user self prefix - automatic database and cache update
  async clearSelfPrefix(userId) {
    return await this.cache.setField(userId, 'SelfPrefix', null);
  }

  // Get user timezone - automatic cache handling
  async getTimezone(userId) {
    return await this.cache.getField(userId, 'Timezone');
  }

  // Set user timezone - automatic database and cache update
  async setTimezone(userId, timezone) {
    return await this.cache.setField(userId, 'Timezone', timezone);
  }

  // Get TicTacToe stats - automatic cache handling
  async getTicTacToeStats(userId) {
    const stats = await this.cache.getField(userId, 'TicTacToe');
    return stats || { Wins: 0, Losses: 0, Matches: 0 };
  }

  // Update TicTacToe stats - automatic database and cache update
  async updateTicTacToeStats(userId, result) {
    const currentStats = await this.getTicTacToeStats(userId);

    // Update stats based on result
    const newStats = {
      Wins: currentStats.Wins + (result === 'win' ? 1 : 0),
      Losses: currentStats.Losses + (result === 'loss' ? 1 : 0),
      Matches: currentStats.Matches + 1
    };

    return await this.cache.setField(userId, 'TicTacToe', newStats);
  }

  // Get TicTacToe win ratio
  async getTicTacToeRatio(userId) {
    const stats = await this.getTicTacToeStats(userId);
    if (stats.Losses === 0) {
      return stats.Wins > 0 ? stats.Wins : 0;
    }
    return parseFloat((stats.Wins / stats.Losses).toFixed(2));
  }

  // ===== LAST.FM LIBRARY METHODS =====

  // Get Last.fm library data
  async getLastFMLibrary(userId) {
    return await this.cache.getField(userId, 'LastFMLibrary');
  }

  // Set Last.fm library data
  async setLastFMLibrary(userId, libraryData) {
    return await this.cache.setField(userId, 'LastFMLibrary', libraryData);
  }

  // Get Last.fm settings
  async getLastFMSettings(userId) {
    return await this.cache.getField(userId, 'LastFMSettings');
  }

  // Set Last.fm custom mode
  async setLastFMCustomMode(userId, customMode) {
    const settings = await this.getLastFMSettings(userId) || {};
    settings.customMode = customMode;
    return await this.cache.setField(userId, 'LastFMSettings', settings);
  }

  // Set Last.fm reactions
  async setLastFMReactions(userId, upvote, downvote) {
    const settings = await this.getLastFMSettings(userId) || {};
    settings.reactions = { upvote, downvote };
    return await this.cache.setField(userId, 'LastFMSettings', settings);
  }

  // Get Last.fm artist plays
  async getLastFMArtistPlays(userId, artistName) {
    const library = await this.getLastFMLibrary(userId);
    if (!library || !library.artists) return 0;

    const artist = library.artists.find(a => a.name.toLowerCase() === artistName.toLowerCase());
    return artist ? artist.plays : 0;
  }

  // Get Last.fm album plays
  async getLastFMAlbumPlays(userId, albumName, artistName) {
    const library = await this.getLastFMLibrary(userId);
    if (!library || !library.albums) return 0;

    const album = library.albums.find(a =>
      a.name.toLowerCase() === albumName.toLowerCase() &&
      a.artist.toLowerCase() === artistName.toLowerCase()
    );
    return album ? album.plays : 0;
  }

  // Get Last.fm track plays
  async getLastFMTrackPlays(userId, trackName, artistName) {
    const library = await this.getLastFMLibrary(userId);
    if (!library || !library.tracks) return 0;

    const track = library.tracks.find(t =>
      t.name.toLowerCase() === trackName.toLowerCase() &&
      t.artist.toLowerCase() === artistName.toLowerCase()
    );
    return track ? track.plays : 0;
  }

  // ===== GAMBLING METHODS =====

  // Get gambling data - automatic cache handling
  async getGamblingData(userId) {
    const gambling = await this.cache.getField(userId, 'Gambling');
    if (!gambling) {
      // Return default gambling data if none exists
      return {
        wallet: 1000,
        bank: 0,
        dailyClaim: {
          lastClaimed: null,
          streak: 0
        },
        robCooldown: null,
        lastWorked: null,
        begCooldown: null,
        lastInterestClaim: null,
        totalGambled: 0,
        totalWon: 0,
        totalLost: 0
      };
    }
    return gambling;
  }

  // Set gambling data - automatic database and cache update
  async setGamblingData(userId, gamblingData) {
    return await this.cache.setField(userId, 'Gambling', gamblingData);
  }

  // Get wallet balance
  async getWalletBalance(userId) {
    const gambling = await this.getGamblingData(userId);
    return gambling.wallet || 0;
  }

  // Get bank balance
  async getBankBalance(userId) {
    const gambling = await this.getGamblingData(userId);
    return gambling.bank || 0;
  }

  // Update wallet balance
  async updateWalletBalance(userId, amount) {
    const gambling = await this.getGamblingData(userId);
    gambling.wallet = Math.max(0, (gambling.wallet || 0) + amount);
    return await this.setGamblingData(userId, gambling);
  }

  // Update bank balance
  async updateBankBalance(userId, amount) {
    const gambling = await this.getGamblingData(userId);
    gambling.bank = Math.max(0, (gambling.bank || 0) + amount);
    return await this.setGamblingData(userId, gambling);
  }

  // Transfer between wallet and bank
  async transferMoney(userId, amount, fromWallet = true) {
    const gambling = await this.getGamblingData(userId);

    if (fromWallet) {
      // Wallet to bank (deposit)
      if (gambling.wallet < amount) return false;
      gambling.wallet -= amount;
      gambling.bank += amount;
    } else {
      // Bank to wallet (withdraw)
      if (gambling.bank < amount) return false;
      gambling.bank -= amount;
      gambling.wallet += amount;
    }

    await this.setGamblingData(userId, gambling);
    return true;
  }

  // Update daily claim
  async updateDailyClaim(userId, streak = 0) {
    const gambling = await this.getGamblingData(userId);
    gambling.dailyClaim = {
      lastClaimed: new Date(),
      streak: streak
    };
    return await this.setGamblingData(userId, gambling);
  }

  // Update rob cooldown
  async updateRobCooldown(userId) {
    const gambling = await this.getGamblingData(userId);
    gambling.robCooldown = new Date();
    return await this.setGamblingData(userId, gambling);
  }

  // Update gambling stats
  async updateGamblingStats(userId, gambled, won, lost) {
    const gambling = await this.getGamblingData(userId);
    gambling.totalGambled = (gambling.totalGambled || 0) + gambled;
    gambling.totalWon = (gambling.totalWon || 0) + won;
    gambling.totalLost = (gambling.totalLost || 0) + lost;
    return await this.setGamblingData(userId, gambling);
  }

  // Update beg cooldown
  async updateBegCooldown(userId) {
    const gambling = await this.getGamblingData(userId);
    gambling.begCooldown = new Date();
    return await this.setGamblingData(userId, gambling);
  }

  // Update work timestamp
  async updateWorkTimestamp(userId) {
    const gambling = await this.getGamblingData(userId);
    gambling.lastWorked = Date.now();
    return await this.setGamblingData(userId, gambling);
  }

  // Calculate and apply bank interest (5% daily interest capped at 100,000 coins gain)
  async calculateBankInterest(userId) {
    const gambling = await this.getGamblingData(userId);
    const bankBalance = gambling.bank || 0;

    if (bankBalance <= 0) return 0;

    const lastInterestClaim = gambling.lastInterestClaim ? new Date(gambling.lastInterestClaim) : null;
    const now = new Date();

    if (!lastInterestClaim) {
      // First time - set the timestamp but don't give interest
      gambling.lastInterestClaim = now;
      await this.setGamblingData(userId, gambling);
      return 0;
    }

    // Calculate hours passed since last interest claim
    const hoursPassed = (now - lastInterestClaim) / (1000 * 60 * 60);

    if (hoursPassed < 24) return 0; // Less than 24 hours passed

    // Calculate days passed (for multiple days of interest)
    const daysPassed = Math.floor(hoursPassed / 24);

    // Calculate interest: 5% per day, capped at 100,000 coins gain per day
    let totalInterest = 0;
    let currentBalance = bankBalance;

    for (let day = 0; day < daysPassed; day++) {
      const dailyInterest = Math.floor(currentBalance * 0.05); // 5% of current balance
      const cappedInterest = Math.min(dailyInterest, 100000); // Cap at 100,000 coins
      totalInterest += cappedInterest;
      currentBalance += cappedInterest; // Compound for next day
    }

    if (totalInterest > 0) {
      // Apply interest to bank
      gambling.bank += totalInterest;
      gambling.lastInterestClaim = now;
      await this.setGamblingData(userId, gambling);
    }

    return totalInterest;
  }

  // Check if user has a gambling account (exists in database/cache)
  async hasGamblingAccount(userId) {
    try {
      // First check if the user exists in cache
      let user = await this.cache.get(userId);

      // If not in cache, check database directly
      if (!user) {
        const mongoose = require('mongoose');
        const User = mongoose.model('User');
        user = await User.findOne({ userId: userId });
        if (!user) return false;
      }

      // Check if the user has gambling data
      const gambling = user.Gambling;
      return gambling !== null && gambling !== undefined;
    } catch (error) {
      console.error('Error checking gambling account:', error);
      return false;
    }
  }

  // ===== ROLEPLAY METHODS =====

  // Get roleplay data - automatic cache handling
  async getRoleplayData(userId) {
    const roleplay = await this.cache.getField(userId, 'Roleplay');
    if (!roleplay) {
      // Return default roleplay data if none exists
      return {
        given: [],
        received: []
      };
    }
    return roleplay;
  }

  // Set roleplay data - automatic database and cache update
  async setRoleplayData(userId, roleplayData) {
    return await this.cache.setField(userId, 'Roleplay', roleplayData);
  }

  // Track a roleplay interaction
  async trackRoleplayInteraction(fromUserId, toUserId, action) {
    // Update the giver's data
    const giverData = await this.getRoleplayData(fromUserId);
    const existingGiven = giverData.given.find(entry =>
      entry.targetUserId === toUserId && entry.action === action
    );

    if (existingGiven) {
      existingGiven.count += 1;
    } else {
      giverData.given.push({
        targetUserId: toUserId,
        action: action,
        count: 1
      });
    }

    await this.setRoleplayData(fromUserId, giverData);

    // Update the receiver's data
    const receiverData = await this.getRoleplayData(toUserId);
    const existingReceived = receiverData.received.find(entry =>
      entry.fromUserId === fromUserId && entry.action === action
    );

    if (existingReceived) {
      existingReceived.count += 1;
    } else {
      receiverData.received.push({
        fromUserId: fromUserId,
        action: action,
        count: 1
      });
    }

    await this.setRoleplayData(toUserId, receiverData);

    // Return the count for the interaction
    return existingGiven ? existingGiven.count : 1;
  }

  // Get interaction count between two users for a specific action
  async getRoleplayCount(fromUserId, toUserId, action) {
    const roleplayData = await this.getRoleplayData(fromUserId);
    const interaction = roleplayData.given.find(entry =>
      entry.targetUserId === toUserId && entry.action === action
    );
    return interaction ? interaction.count : 0;
  }

  // Get total times a user was targeted with a specific action
  async getTotalRoleplayReceived(userId, action) {
    const roleplayData = await this.getRoleplayData(userId);
    return roleplayData.received
      .filter(entry => entry.action === action)
      .reduce((total, entry) => total + entry.count, 0);
  }

  // ===== NOTES METHODS =====

  // Get notes data - automatic cache handling
  async getNotesData(userId) {
    const notes = await this.cache.getField(userId, 'Notes');
    if (!notes) {
      // Return empty array if no notes exist
      return [];
    }
    return notes;
  }

  // Set notes data - automatic database and cache update
  async setNotesData(userId, notesData) {
    return await this.cache.setField(userId, 'Notes', notesData);
  }

  // Add a new note
  async addNote(userId, content) {
    const notes = await this.getNotesData(userId);

    // Generate new ID (highest existing ID + 1, or 1 if no notes)
    const newId = notes.length > 0 ? Math.max(...notes.map(note => note.id)) + 1 : 1;

    const newNote = {
      id: newId,
      content: content,
      createdAt: new Date()
    };

    notes.push(newNote);
    await this.setNotesData(userId, notes);

    return newNote;
  }

  // Remove a note by ID
  async removeNote(userId, noteId) {
    const notes = await this.getNotesData(userId);
    const noteIndex = notes.findIndex(note => note.id === noteId);

    if (noteIndex === -1) {
      return false; // Note not found
    }

    const removedNote = notes[noteIndex];
    notes.splice(noteIndex, 1);
    await this.setNotesData(userId, notes);

    return removedNote;
  }

  // Get a specific note by ID
  async getNote(userId, noteId) {
    const notes = await this.getNotesData(userId);
    return notes.find(note => note.id === noteId) || null;
  }

  // Clear all notes for a user
  async clearAllNotes(userId) {
    return await this.setNotesData(userId, []);
  }

  // ===== NAME HISTORY METHODS =====

  // Get name history data - automatic cache handling
  async getNameHistory(userId) {
    const nameHistory = await this.cache.getField(userId, 'NameHistory');
    if (!nameHistory) {
      return [];
    }
    return nameHistory;
  }

  // Set name history data - automatic database and cache update
  async setNameHistory(userId, nameHistoryData) {
    return await this.cache.setField(userId, 'NameHistory', nameHistoryData);
  }

  // Add a name change entry
  async addNameChange(userId, type, oldName, newName, guildId = null) {
    const nameHistory = await this.getNameHistory(userId);

    const nameChange = {
      type: type, // 'username', 'displayname', or 'nickname'
      oldName: oldName,
      newName: newName,
      guildId: guildId,
      changedAt: new Date()
    };

    nameHistory.push(nameChange);

    // Keep only the last 50 entries to prevent unlimited growth
    if (nameHistory.length > 50) {
      nameHistory.splice(0, nameHistory.length - 50);
    }

    await this.setNameHistory(userId, nameHistory);
    return nameChange;
  }

  // Get name history filtered by type
  async getNameHistoryByType(userId, type) {
    const nameHistory = await this.getNameHistory(userId);
    return nameHistory.filter(entry => entry.type === type);
  }

  // Get name history for a specific guild (nicknames)
  async getNameHistoryByGuild(userId, guildId) {
    const nameHistory = await this.getNameHistory(userId);
    return nameHistory.filter(entry => entry.guildId === guildId);
  }

  // Clear all name history for a user
  async clearAllNameHistory(userId) {
    return await this.setNameHistory(userId, []);
  }

  // ===== BIRTHDAY METHODS =====

  // Get birthday data - automatic cache handling
  async getBirthday(userId) {
    const birthday = await this.cache.getField(userId, 'Birthday');
    if (!birthday || !birthday.day || !birthday.month) {
      return null;
    }
    return birthday;
  }

  // Set birthday - automatic database and cache update
  async setBirthday(userId, day, month) {
    const birthdayData = {
      day: day,
      month: month,
      setAt: new Date()
    };
    return await this.cache.setField(userId, 'Birthday', birthdayData);
  }

  // Clear birthday - automatic database and cache update
  async clearBirthday(userId) {
    const birthdayData = {
      day: null,
      month: null,
      setAt: null
    };
    return await this.cache.setField(userId, 'Birthday', birthdayData);
  }

  // Check if today is user's birthday
  async isBirthday(userId) {
    const birthday = await this.getBirthday(userId);
    if (!birthday) return false;

    const today = new Date();
    return today.getDate() === birthday.day && (today.getMonth() + 1) === birthday.month;
  }

  // Get formatted birthday string
  async getFormattedBirthday(userId) {
    const birthday = await this.getBirthday(userId);
    if (!birthday) return null;

    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Format as "March 02" with zero-padded day
    const day = birthday.day.toString().padStart(2, '0');
    return `${months[birthday.month - 1]} ${day}`;
  }

  // Utility methods
  invalidateUser(userId) {
    this.cache.invalidate(userId);
  }

  clearCache() {
    this.cache.clear();
  }

  getStats() {
    return {
      users: this.cache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new User();
